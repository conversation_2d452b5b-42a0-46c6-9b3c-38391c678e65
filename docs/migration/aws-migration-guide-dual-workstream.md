# AWS Migration Guide for Bon Voyage - Dual Workstream Approach

This guide outlines the migration strategy using **two specialized workstreams** working in parallel:
- **Workstream A**: AWS Infrastructure & DevOps Specialist
- **Workstream B**: WordPress Developer & Content Migration Specialist

## Executive Summary

**Migration Approach**: WordPress + Gravity Forms + ACF (Highly Recommended)
**Timeline**: 12-16 days with parallel workstreams
**Team Structure**: 2 senior specialists working collaboratively
**Budget Estimate**: £9,000 - £12,000 (based on £500/day rate)

## Current Architecture Overview

- **CakePHP Application**: PHP 7.2 with Apache, document root at `app/webroot`
- **WordPress Blog**: Integrated at `/blog` path, using Timber framework
- **Database**: MariaDB 10.11 (MySQL compatible)
- **Current Deployment**: Elastic Beanstalk (existing `.ebextensions` configs found)

## Critical Infrastructure Risks

### ⚠️ URGENT: Platform End-of-Life Issues
1. **PHP 7.2**: No longer supported by AWS Elastic Beanstalk
2. **Node.js 0.12**: Severe security vulnerabilities, no updates since 2016
3. **CakePHP 1.2.12**: No security updates since 2012
4. **Amazon Linux AMI**: Legacy images may be removed within 2 years

## Dual Workstream Strategy

### **Workstream A: AWS Infrastructure Specialist**
**Focus**: Infrastructure, hosting, deployment, and DevOps
**Duration**: 8-10 days (spread over 12-16 day project timeline)
**Allocation**: 60-70% of total project time

### **Workstream B: WordPress Developer**
**Focus**: Application development, content migration, and testing
**Duration**: 12-16 days (full project timeline)
**Allocation**: 100% of project timeline

### **Collaboration Points**
- **Daily standups** (15 minutes)
- **Integration checkpoints** at key milestones
- **Shared deployment and testing phases**

## Workstream A: AWS Infrastructure & DevOps

### **Responsibilities**

#### **Phase 1: Infrastructure Assessment (Days 1-2)**
- Analyze current Elastic Beanstalk configuration
- Document existing VPC, subnets, and security groups
- Review current EFS and S3 setup
- Plan new infrastructure architecture
- **Deliverable**: Infrastructure migration plan

#### **Phase 2: Environment Setup (Days 3-5)**
- Create new RDS MySQL 8.0 instance
- Set up S3 buckets for WordPress media storage
- Configure CloudFront CDN for performance
- Establish VPC and security group configurations
- **Deliverable**: Staging environment ready

#### **Phase 3: Database Migration (Days 6-7)**
- Export current MariaDB database
- Transform schema for WordPress compatibility
- Import and validate data integrity
- Set up database backup procedures
- **Deliverable**: Migrated database in staging

#### **Phase 4: Deployment Pipeline (Days 8-9)**
- Configure Elastic Beanstalk for WordPress
- Set up CI/CD pipeline for deployments
- Implement monitoring and alerting
- Configure SSL certificates and domain routing
- **Deliverable**: Production-ready deployment pipeline

#### **Phase 5: Go-Live Support (Days 10)**
- Execute production deployment
- Monitor system performance
- Resolve any infrastructure issues
- Handover documentation and access
- **Deliverable**: Live production environment

### **Technical Deliverables**
- AWS infrastructure as code (CloudFormation/Terraform)
- Database migration scripts with validation
- Deployment automation scripts
- Monitoring and alerting configuration
- Security and backup procedures documentation

## Workstream B: WordPress Development & Content Migration

### **Responsibilities**

#### **Phase 1: Analysis & Planning (Days 1-3)**
- Analyze CakePHP codebase and database schema
- Document current functionality and integrations
- Plan WordPress architecture (custom post types, ACF fields)
- Design content migration strategy
- **Deliverable**: Development plan and WordPress architecture

#### **Phase 2: WordPress Setup (Days 4-6)**
- Install and configure WordPress
- Develop custom theme matching current design
- Set up Advanced Custom Fields (ACF) structure
- Configure Gravity Forms for lead generation
- **Deliverable**: WordPress foundation ready

#### **Phase 3: Content Migration (Days 7-10)**
- Develop automated migration scripts
- Migrate destinations, accommodation, activities
- Transfer images and media files
- Set up navigation and menu structures
- **Deliverable**: Content migrated to WordPress

#### **Phase 4: Integration & Testing (Days 11-13)**
- Integrate third-party services (Feefo, Google APIs)
- Implement search functionality
- Test all forms and lead generation
- Perform cross-browser and mobile testing
- **Deliverable**: Fully functional WordPress site

#### **Phase 5: Final Testing & Launch (Days 14-16)**
- User acceptance testing
- Performance optimization
- SEO validation and redirects
- Content editor training
- **Deliverable**: Production-ready WordPress application

### **Technical Deliverables**
- Custom WordPress theme
- Content migration scripts
- ACF field configurations
- Gravity Forms setup
- Third-party integration code
- User documentation and training materials

## Workstream Overlap and Collaboration Points

### **Critical Collaboration Phases**

#### **Days 1-2: Joint Planning**
- **Both teams**: Requirements gathering and architecture planning
- **Overlap**: Database schema analysis and WordPress structure planning
- **Coordination**: Daily alignment meetings

#### **Days 6-7: Database Integration**
- **AWS Specialist**: Database setup and migration
- **WordPress Developer**: WordPress database configuration
- **Overlap**: Schema validation and data integrity testing
- **Coordination**: Real-time collaboration on database issues

#### **Days 11-13: Integration Testing**
- **AWS Specialist**: Infrastructure monitoring and optimization
- **WordPress Developer**: Application testing and debugging
- **Overlap**: End-to-end system testing
- **Coordination**: Joint troubleshooting sessions

#### **Days 14-16: Deployment & Go-Live**
- **AWS Specialist**: Production deployment and monitoring
- **WordPress Developer**: Final testing and user training
- **Overlap**: Launch coordination and issue resolution
- **Coordination**: Continuous communication during go-live

## Timeline Dependencies

### **WordPress Developer Dependencies on AWS:**
- **Day 4**: Staging environment ready for WordPress installation
- **Day 7**: Database migration completed for content migration testing
- **Day 11**: Production environment ready for final testing
- **Day 14**: Deployment pipeline ready for go-live

### **AWS Specialist Dependencies on WordPress:**
- **Day 3**: WordPress requirements finalized for infrastructure planning
- **Day 6**: WordPress database schema ready for migration
- **Day 10**: WordPress application ready for production deployment
- **Day 13**: Final application testing completed for go-live approval

## Risk Management

### **Workstream Coordination Risks**
- **Communication gaps**: Mitigated by daily standups and shared documentation
- **Dependency delays**: Buffer time built into critical path items
- **Integration issues**: Joint testing phases and real-time collaboration
- **Knowledge silos**: Cross-training and documentation sharing

### **Technical Risk Mitigation**
- **Database migration**: Comprehensive backup and rollback procedures
- **Infrastructure changes**: Staging environment for testing
- **Application bugs**: Parallel development and testing environments
- **Performance issues**: Load testing and optimization phases

## Budget Allocation

### **Workstream A (AWS Specialist): £4,000 - £5,000**
- 8-10 days at £500/day
- Infrastructure setup and deployment focus
- 60-70% allocation during overlap periods

### **Workstream B (WordPress Developer): £6,000 - £8,000**
- 12-16 days at £500/day
- Full-time development and migration focus
- 100% allocation throughout project

### **Total Project Cost: £10,000 - £13,000**
- Includes 10% contingency buffer
- Excludes AWS infrastructure costs (estimated £100-200/month)

## Success Metrics

### **Technical Success Criteria**
- 100% data migration accuracy
- Site performance ≤ 3 seconds page load
- 99.9% uptime post-launch
- All functionality preserved or improved

### **Project Success Criteria**
- Delivered within 12-16 day timeline
- Budget adherence within 10%
- Smooth user transition
- Complete documentation handover

## Next Steps

1. **Team Assembly**: Identify and onboard AWS specialist and WordPress developer
2. **Project Kickoff**: Joint planning session with both workstreams
3. **Environment Setup**: AWS specialist begins infrastructure work
4. **Development Start**: WordPress developer begins analysis and planning
5. **Regular Coordination**: Daily standups and weekly progress reviews

This dual workstream approach optimizes expertise while maintaining clear accountability and efficient collaboration between infrastructure and application development teams.

## Gantt Chart Timeline - Dual Workstream Approach

### **16-Day Project Timeline Overview**

```
Day:  1  2  3  4  5  6  7  8  9 10 11 12 13 14 15 16
      |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |
AWS:  ████████████████████████████████████████████████
WP:   ████████████████████████████████████████████████████████████████
```

### **Detailed Gantt Chart - Workstream A (AWS Infrastructure)**

```
Phase & Tasks                    Days: 1  2  3  4  5  6  7  8  9 10 11 12 13 14 15 16
                                      |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |

PHASE 1: Infrastructure Assessment
├─ Analyze current EB config          ██
├─ Document VPC/security groups       ██
└─ Plan new architecture              ██

PHASE 2: Environment Setup
├─ Create RDS MySQL instance             ██
├─ Set up S3 buckets                     ██ ██
├─ Configure CloudFront CDN              ██ ██
└─ VPC/security configuration               ██

PHASE 3: Database Migration
├─ Export current database                     ██
├─ Schema transformation                       ██
├─ Import & validate data                      ██
└─ Backup procedures                           ██

PHASE 4: Deployment Pipeline
├─ Configure EB for WordPress                     ██
├─ Set up CI/CD pipeline                          ██
├─ Monitoring & alerting                          ██
└─ SSL & domain config                            ██

PHASE 5: Go-Live Support
├─ Production deployment                             ██
├─ Performance monitoring                            ██
├─ Issue resolution                                  ██ ██ ██
└─ Documentation handover                               ██ ██

COLLABORATION POINTS
├─ Joint planning sessions            ██
├─ Database integration                           ██ ██
├─ Integration testing                                  ██ ██ ██
└─ Deployment coordination                                 ██ ██ ██
```

### **Detailed Gantt Chart - Workstream B (WordPress Development)**

```
Phase & Tasks                    Days: 1  2  3  4  5  6  7  8  9 10 11 12 13 14 15 16
                                      |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |

PHASE 1: Analysis & Planning
├─ CakePHP codebase analysis          ██
├─ Database schema analysis           ██ ██
├─ WordPress architecture plan           ██
└─ Content migration strategy            ██

PHASE 2: WordPress Setup
├─ WordPress installation                   ██ (wait for staging)
├─ Custom theme development                 ██ ██
├─ ACF structure setup                         ██
└─ Gravity Forms configuration                 ██

PHASE 3: Content Migration
├─ Migration script development                   ██ (wait for DB)
├─ Destinations migration                         ██ ██
├─ Media files transfer                              ██
└─ Navigation setup                                  ██

PHASE 4: Integration & Testing
├─ Third-party integrations                             ██
├─ Search functionality                                 ██ ██
├─ Forms testing                                           ██
└─ Cross-browser testing                                   ██

PHASE 5: Final Testing & Launch
├─ User acceptance testing                                    ██
├─ Performance optimization                                   ██
├─ SEO validation                                             ██
└─ Content editor training                                       ██

COLLABORATION POINTS
├─ Joint planning sessions            ██
├─ Database integration                           ██ ██
├─ Integration testing                                  ██ ██ ██
└─ Deployment coordination                                 ██ ██ ██

DEPENDENCIES (Waiting periods)
├─ Wait for staging environment          ⏸️
├─ Wait for database migration              ⏸️
├─ Wait for production environment                    ⏸️
└─ Wait for deployment pipeline                          ⏸️
```

### **Critical Path Analysis**

#### **Longest Path (16 days)**: WordPress Development Workstream
```
Day 1-3:  Analysis & Planning →
Day 4-6:  WordPress Setup →
Day 7-10: Content Migration →
Day 11-13: Integration & Testing →
Day 14-16: Final Testing & Launch
```

#### **AWS Workstream (10 days active, 6 days support)**
```
Day 1-2:  Infrastructure Assessment →
Day 3-5:  Environment Setup →
Day 6-7:  Database Migration →
Day 8-9:  Deployment Pipeline →
Day 10:   Go-Live Support
Day 11-16: Monitoring & Support (as needed)
```

### **Overlap Periods and Coordination**

#### **High Collaboration Days:**
- **Days 1-2**: Joint planning and requirements gathering
- **Days 6-7**: Database migration and WordPress integration
- **Days 11-13**: Integration testing and troubleshooting
- **Days 14-16**: Deployment coordination and go-live support

#### **Independent Work Periods:**
- **Days 3-5**: AWS environment setup / WordPress theme development
- **Days 8-10**: AWS pipeline setup / WordPress content migration

### **Resource Utilization Chart**

```
Resource Allocation by Day:

Day:     1   2   3   4   5   6   7   8   9  10  11  12  13  14  15  16
AWS:    100% 100% 100% 100% 100% 100% 100% 100% 100% 100% 50% 50% 50% 75% 75% 25%
WP:     100% 100% 100% 100% 100% 100% 100% 100% 100% 100% 100% 100% 100% 100% 100% 100%

Total:  200% 200% 200% 200% 200% 200% 200% 200% 200% 200% 150% 150% 150% 175% 175% 125%
```

### **Budget Distribution Timeline**

```
Daily Cost Breakdown (£500/day rate):

Day:     1    2    3    4    5    6    7    8    9   10   11   12   13   14   15   16
AWS:   £500 £500 £500 £500 £500 £500 £500 £500 £500 £500 £250 £250 £250 £375 £375 £125
WP:    £500 £500 £500 £500 £500 £500 £500 £500 £500 £500 £500 £500 £500 £500 £500 £500

Daily: £1000 £1000 £1000 £1000 £1000 £1000 £1000 £1000 £1000 £1000 £750 £750 £750 £875 £875 £625

Cumulative Total: £10,000 + £2,000 (contingency) = £12,000
```

This timeline visualization clearly shows how the two workstreams complement each other, with the AWS specialist front-loading infrastructure work and providing support during integration, while the WordPress developer maintains consistent progress throughout the entire project timeline.

## AWS Infrastructure Configuration Details

### **Current AWS Environment (From Off-boarding Documentation)**

#### **Elastic Beanstalk Configuration:**
- **Environment Name**: `Bvwww-env-1`
- **Platform**: PHP 7.2 on Amazon Linux (⚠️ EOL)
- **Auto Scaling**: 1-2 instances, scales at 70% CPU usage
- **Region**: eu-west-2 (London)

#### **Network Configuration:**
```yaml
VPC ID: vpc-3f2dae57
Subnets:
  - subnet-51b92838 (AZ A)
  - subnet-756dbf0f (AZ B)
  - subnet-de37d792 (AZ C)
Security Group: sg-0e826ec64a13a2a54
```

#### **Current Storage Setup:**
- **EFS**: Mounted to `/resources` for shared file storage
- **S3 Bucket**: `bv-www-resources` (synced via cron every minute)
- **CloudFront CDN**:
  - CSS/JS: `assets.bon-voyage.co.uk`
  - Images: `resources.bon-voyage.co.uk`

### **Target AWS Architecture**

#### **Recommended Infrastructure Stack:**
```yaml
# New Production Environment
Platform: PHP 8.1 on Amazon Linux 2023
Database: RDS MySQL 8.0 (Multi-AZ)
Storage: S3 + EFS hybrid approach
CDN: CloudFront with optimized caching
Monitoring: CloudWatch + AWS X-Ray
Backup: Automated RDS snapshots + S3 versioning
```

#### **WordPress Media Storage: S3 + EFS Configuration**

**Architecture Overview:**
WordPress images stored in S3 but mounted to EBS volume for transparent access.

**S3 Bucket Setup:**
```bash
# Create S3 bucket for WordPress media
aws s3 mb s3://bon-voyage-wp-media --region eu-west-2

# Configure bucket policy for web access
aws s3api put-bucket-policy --bucket bon-voyage-wp-media --policy '{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::bon-voyage-wp-media/*"
    }
  ]
}'
```

**S3FS Configuration for EBS Mounting:**
```bash
# .ebextensions/s3fs-setup.config
packages:
  yum:
    fuse: []
    fuse-devel: []
    gcc-c++: []

commands:
  01_install_s3fs:
    command: |
      cd /tmp
      git clone https://github.com/s3fs-fuse/s3fs-fuse.git
      cd s3fs-fuse
      ./autogen.sh && ./configure && make && make install

  02_mount_s3_bucket:
    command: |
      mkdir -p /var/www/html/app/webroot/blog/app/uploads
      s3fs bon-voyage-wp-media /var/www/html/app/webroot/blog/app/uploads \
        -o passwd_file=/etc/passwd-s3fs \
        -o url=https://s3.eu-west-2.amazonaws.com \
        -o use_cache=/tmp/s3fs-cache \
        -o allow_other -o uid=33 -o gid=33
```

### **Database Migration Strategy**

#### **Current Database Analysis:**
- **Engine**: MariaDB 10.11 (MySQL compatible)
- **Size**: 30+ tables with complex relationships
- **Key Tables**: destinations (589 records), accommodation (671), activities (719)

#### **Migration Approach:**
```sql
-- 1. Export current database
mysqldump --single-transaction --routines --triggers bon_voyage > backup.sql

-- 2. Create new RDS instance
aws rds create-db-instance \
  --db-instance-identifier bon-voyage-prod \
  --db-instance-class db.t3.small \
  --engine mysql \
  --engine-version 8.0 \
  --allocated-storage 50 \
  --master-username admin \
  --master-user-password [secure-password]

-- 3. Import with compatibility adjustments
mysql -h rds-endpoint -u admin -p bon_voyage_wp < backup.sql
```

### **Performance Optimization Configuration**

#### **CloudFront Distribution Setup:**
```yaml
Origins:
  - Domain: bon-voyage-wp-media.s3.eu-west-2.amazonaws.com
    Path: /
    CustomHeaders:
      - HeaderName: Cache-Control
        HeaderValue: max-age=31536000

Behaviors:
  - PathPattern: "*.jpg"
    CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad # Managed-CachingOptimized
  - PathPattern: "*.css"
    CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
  - PathPattern: "*.js"
    CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
```

#### **Elastic Beanstalk Optimization:**
```yaml
# .ebextensions/performance.config
option_settings:
  aws:elasticbeanstalk:environment:proxy:staticfiles:
    /assets: app/webroot/css
    /js: app/webroot/js
    /img: app/webroot/img

  aws:autoscaling:launchconfiguration:
    InstanceType: t3.medium

  aws:autoscaling:asg:
    MinSize: 2
    MaxSize: 6

  aws:autoscaling:trigger:
    MeasureName: CPUUtilization
    Unit: Percent
    UpperThreshold: 75
    LowerThreshold: 25
```

### **Security Configuration**

#### **IAM Roles and Policies:**
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject"
      ],
      "Resource": "arn:aws:s3:::bon-voyage-wp-media/*"
    },
    {
      "Effect": "Allow",
      "Action": ["rds:DescribeDBInstances"],
      "Resource": "*"
    }
  ]
}
```

#### **Security Groups Configuration:**
```yaml
# Application Security Group
ApplicationSG:
  Type: AWS::EC2::SecurityGroup
  Properties:
    GroupDescription: Security group for Bon Voyage application
    VpcId: vpc-3f2dae57
    SecurityGroupIngress:
      - IpProtocol: tcp
        FromPort: 80
        ToPort: 80
        CidrIp: 0.0.0.0/0
      - IpProtocol: tcp
        FromPort: 443
        ToPort: 443
        CidrIp: 0.0.0.0/0

# Database Security Group
DatabaseSG:
  Type: AWS::EC2::SecurityGroup
  Properties:
    GroupDescription: Security group for RDS database
    VpcId: vpc-3f2dae57
    SecurityGroupIngress:
      - IpProtocol: tcp
        FromPort: 3306
        ToPort: 3306
        SourceSecurityGroupId: !Ref ApplicationSG
```

### **Monitoring and Alerting Setup**

#### **CloudWatch Alarms:**
```yaml
# High CPU Usage
CPUAlarm:
  Type: AWS::CloudWatch::Alarm
  Properties:
    AlarmName: BonVoyage-HighCPU
    MetricName: CPUUtilization
    Namespace: AWS/EC2
    Statistic: Average
    Period: 300
    EvaluationPeriods: 2
    Threshold: 80
    ComparisonOperator: GreaterThanThreshold

# Database Connection Count
DBConnectionAlarm:
  Type: AWS::CloudWatch::Alarm
  Properties:
    AlarmName: BonVoyage-HighDBConnections
    MetricName: DatabaseConnections
    Namespace: AWS/RDS
    Statistic: Average
    Period: 300
    Threshold: 40
    ComparisonOperator: GreaterThanThreshold
```

### **Backup and Disaster Recovery**

#### **Automated Backup Strategy:**
```yaml
# RDS Automated Backups
BackupRetentionPeriod: 7
PreferredBackupWindow: "03:00-04:00"
PreferredMaintenanceWindow: "sun:04:00-sun:05:00"

# S3 Lifecycle Policy
LifecycleConfiguration:
  Rules:
    - Status: Enabled
      Transitions:
        - Days: 30
          StorageClass: STANDARD_IA
        - Days: 90
          StorageClass: GLACIER
```

This comprehensive AWS configuration provides the infrastructure foundation that the AWS specialist will implement during their workstream.
