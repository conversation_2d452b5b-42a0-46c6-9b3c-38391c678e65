# AWS Migration Guide for Bon Voyage - Comprehensive Dual Workstream Approach

## Table of Contents

1. [Executive Summary](#executive-summary)
   - [Project Overview](#project-overview)
   - [Timeline and Cost Breakdown](#timeline-and-cost-breakdown)
   - [Risk Assessment](#risk-assessment)
   - [Recommendations](#recommendations)

2. [Technical Implementation](#technical-implementation)
   - [Workstream A: AWS Infrastructure & DevOps](#workstream-a-aws-infrastructure--devops)
   - [Workstream B: WordPress Development & Migration](#workstream-b-wordpress-development--migration)
   - [Database Migration Analysis](#database-migration-analysis)
   - [CakePHP Migration Feasibility](#cakephp-migration-feasibility)
   - [Blog Migration: Bedrock/Timber to Vanilla WordPress](#blog-migration-bedrocktimber-to-vanilla-wordpress)
   - [Project Management Framework](#project-management-framework)

---

## Executive Summary

### Project Overview

**Migration Strategy**: Complete modernization from legacy CakePHP 1.2.12 to WordPress + ACF + Gravity Forms
**Approach**: Dual workstream with specialized AWS and WordPress experts working in parallel
**Duration**: 12-16 days with optimized parallel execution
**Team Structure**: 2 senior specialists (AWS Infrastructure + WordPress Development)

#### **Current System Analysis**
- **CakePHP 1.2.12**: End-of-life framework with no security updates since 2012
- **PHP 7.2**: No longer supported by AWS Elastic Beanstalk
- **Complex Database**: 30+ tables with 50,000+ records and intricate relationships
- **Legacy Infrastructure**: Outdated Node.js 0.12, deprecated Amazon Linux AMI

#### **Target Architecture**
- **WordPress + ACF**: Modern, maintainable content management system
- **PHP 8.1**: Current LTS version with security support
- **AWS Infrastructure**: Modernized Elastic Beanstalk with RDS MySQL 8.0
- **Automated Migration**: 85% automated database conversion scripts

### Timeline and Cost Breakdown

#### **Workstream Timeline Overview**
```
Workstream A (AWS Infrastructure): 8-10 days (60-70% allocation during overlaps)
Workstream B (WordPress Development): 12-16 days (100% allocation)
Total Project Duration: 16 days maximum
```

#### **Detailed Cost Analysis**
| Component | Duration | Daily Rate | Total Cost |
|-----------|----------|------------|------------|
| **AWS Infrastructure Specialist** | 8-10 days | £500/day | £4,000 - £5,000 |
| **WordPress Developer** | 12-16 days | £500/day | £6,000 - £8,000 |
| **Project Contingency (10%)** | - | - | £1,000 - £1,300 |
| **Total Project Cost** | **16 days** | - | **£11,000 - £14,300** |

#### **Monthly Operational Costs (Post-Migration)**
- **AWS Infrastructure**: £90-370/month (depending on traffic)
- **Maintenance & Support**: £400/month (optional retainer)
- **Total Ongoing**: £490-770/month

### Risk Assessment

#### **Critical Risks Mitigated**
- ✅ **Security Vulnerabilities**: Eliminated by migrating from EOL platforms
- ✅ **Data Loss**: Comprehensive backup and validation procedures
- ✅ **Extended Downtime**: Parallel environment with <1 hour rollback capability
- ✅ **Budget Overrun**: Fixed-price phases with clear scope boundaries

#### **Technical Feasibility**
- **Database Migration**: ✅ **HIGHLY FEASIBLE** (85% automated conversion possible)
- **CakePHP Direct Migration**: ❌ **NOT RECOMMENDED** (6-12 months, high risk)
- **WordPress Migration**: ✅ **RECOMMENDED** (3-4 months, low risk)
- **Blog Conversion**: ✅ **SIMPLE** (4-5 days, direct template conversion)

### Recommendations

#### **Primary Recommendation: WordPress + ACF Migration**
- **Fastest Implementation**: 3-4 months vs 6-12 months for CakePHP upgrade
- **Lowest Risk**: Proven migration path with automated scripts
- **Best ROI**: £11,000-14,300 vs £150,000-300,000 for CakePHP migration
- **Future-Proof**: Modern platform with ongoing security updates

#### **Alternative Considered: Laravel Rebuild**
- **Technical Excellence**: Superior architecture and performance
- **Timeline**: 4-6 months implementation
- **Cost**: £80,000-120,000 total project cost
- **Recommendation**: Consider for future major redesign

---

## Technical Implementation

### Workstream A: AWS Infrastructure & DevOps

#### **Phase 1: Infrastructure Assessment and Planning (Days 1-2)**

**Objective**: Analyze current AWS setup and plan modernized infrastructure

**Current Environment Analysis**:
```yaml
# Existing Configuration (From Off-boarding Documentation)
Environment: Bvwww-env-1
Platform: PHP 7.2 on Amazon Linux (⚠️ EOL)
VPC: vpc-3f2dae57
Subnets: 
  - subnet-51b92838 (AZ A)
  - subnet-756dbf0f (AZ B)
  - subnet-de37d792 (AZ C)
Security Group: sg-0e826ec64a13a2a54
Storage: EFS mounted to /resources + S3 bucket bv-www-resources
CDN: CloudFront (assets.bon-voyage.co.uk, resources.bon-voyage.co.uk)
```

**Tasks**:
1. **Document Current Infrastructure**
   - Export Elastic Beanstalk configuration
   - Analyze VPC and security group settings
   - Review EFS and S3 bucket configurations
   - Document CloudFront distribution settings

2. **Plan Target Architecture**
   - Design PHP 8.1 Elastic Beanstalk environment
   - Plan RDS MySQL 8.0 migration strategy
   - Design S3 + EFS hybrid storage for WordPress media
   - Optimize CloudFront for WordPress performance

3. **Risk Assessment**
   - Identify single points of failure
   - Plan backup and rollback procedures
   - Design monitoring and alerting strategy

**Deliverables**:
- Infrastructure migration plan document
- AWS resource inventory and mapping
- Risk mitigation strategy
- Cost optimization recommendations

#### **Phase 2: Environment Setup and Configuration (Days 3-5)**

**Objective**: Create staging and production environments with modern AWS services

**RDS Database Setup**:
```bash
# Create RDS MySQL 8.0 instance
aws rds create-db-instance \
  --db-instance-identifier bon-voyage-prod \
  --db-instance-class db.t3.small \
  --engine mysql \
  --engine-version 8.0.35 \
  --allocated-storage 50 \
  --storage-type gp3 \
  --storage-encrypted \
  --master-username admin \
  --master-user-password [secure-password] \
  --vpc-security-group-ids sg-database-new \
  --db-subnet-group-name bon-voyage-subnet-group \
  --backup-retention-period 7 \
  --multi-az \
  --deletion-protection
```

**S3 Configuration for WordPress Media**:
```bash
# Create S3 bucket for WordPress media storage
aws s3 mb s3://bon-voyage-wp-media --region eu-west-2

# Configure bucket policy for public read access
aws s3api put-bucket-policy --bucket bon-voyage-wp-media --policy '{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow", 
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::bon-voyage-wp-media/*"
    }
  ]
}'

# Enable versioning for backup protection
aws s3api put-bucket-versioning \
  --bucket bon-voyage-wp-media \
  --versioning-configuration Status=Enabled

# Configure lifecycle policy for cost optimization
aws s3api put-bucket-lifecycle-configuration \
  --bucket bon-voyage-wp-media \
  --lifecycle-configuration file://lifecycle-policy.json
```

**Elastic Beanstalk Environment**:
```yaml
# .ebextensions/environment.config
option_settings:
  aws:elasticbeanstalk:application:environment:
    RDS_HOSTNAME: !GetAtt Database.Endpoint.Address
    RDS_PORT: 3306
    RDS_DB_NAME: bon_voyage_wp
    RDS_USERNAME: admin
    WP_ENV: production
    WP_HOME: https://www.bon-voyage.co.uk
    
  aws:autoscaling:launchconfiguration:
    InstanceType: t3.medium
    SecurityGroups: sg-application-new
    
  aws:autoscaling:asg:
    MinSize: 2
    MaxSize: 6
    
  aws:elasticbeanstalk:environment:proxy:staticfiles:
    /wp-content/uploads: wp-content/uploads
```

**Tasks**:
1. **Create RDS Database Instance**
   - Configure Multi-AZ deployment for high availability
   - Set up automated backups and maintenance windows
   - Configure security groups for database access
   - Test connectivity from application servers

2. **Configure S3 Storage**
   - Create WordPress media bucket with public read access
   - Set up lifecycle policies for cost optimization
   - Configure S3FS for transparent file system mounting
   - Test file upload and retrieval functionality

3. **Set Up Elastic Beanstalk**
   - Create new PHP 8.1 environment
   - Configure auto-scaling and load balancing
   - Set up environment variables and secrets
   - Install and configure S3FS for media mounting

**Deliverables**:
- Staging environment ready for WordPress installation
- RDS database configured and accessible
- S3 storage with proper permissions and lifecycle policies
- Monitoring and alerting baseline configuration

#### **Phase 3: Database Migration and Validation (Days 6-7)**

**Objective**: Migrate existing MariaDB data to new RDS MySQL 8.0 instance with validation

**Current Database Analysis**:
```sql
-- Key tables and record counts from existing system
destinations: 589 records (hierarchical tree structure)
accommodation: 671 records (complex relationships)
activities: 719 records (many-to-many with destinations)
images: 7,887 records (extensive media library)
content_blocks: 4,986 records (flexible content system)
quote_requests: 8,981 records (historical form data)
travel_plans: 18 records (new lead generation)
```

**Migration Strategy**:
```bash
#!/bin/bash
# database-migration.sh - Complete database migration script

echo "Starting database migration..."

# 1. Create full backup of current database
mysqldump --single-transaction --routines --triggers \
  --host=current-db-host \
  --user=current-user \
  --password=current-password \
  bon_voyage > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. Analyze schema compatibility
mysql --host=$RDS_HOSTNAME --user=$RDS_USERNAME --password=$RDS_PASSWORD \
  -e "SELECT VERSION();" # Verify MySQL 8.0 connectivity

# 3. Import with compatibility adjustments
sed 's/ENGINE=MyISAM/ENGINE=InnoDB/g' backup_*.sql | \
mysql --host=$RDS_HOSTNAME --user=$RDS_USERNAME --password=$RDS_PASSWORD bon_voyage_wp

# 4. Validate data integrity
php validate-migration.php --source=old-db --target=new-db
```

**Data Validation Script**:
```php
<?php
// validate-migration.php - Comprehensive data validation

class MigrationValidator {

    public function validateMigration() {
        $this->validateRecordCounts();
        $this->validateDataIntegrity();
        $this->validateRelationships();
        $this->validateImageReferences();
    }

    private function validateRecordCounts() {
        $tables = [
            'destinations' => 589,
            'accommodation' => 671,
            'activities' => 719,
            'images' => 7887,
            'content_blocks' => 4986
        ];

        foreach ($tables as $table => $expectedCount) {
            $actualCount = $this->getRecordCount($table);
            if ($actualCount !== $expectedCount) {
                throw new Exception("Record count mismatch in {$table}: expected {$expectedCount}, got {$actualCount}");
            }
            echo "✓ {$table}: {$actualCount} records validated\n";
        }
    }

    private function validateDataIntegrity() {
        // Check for NULL values in required fields
        $requiredFields = [
            'destinations' => ['name', 'slug'],
            'accommodation' => ['name', 'destination_id'],
            'activities' => ['name', 'description']
        ];

        foreach ($requiredFields as $table => $fields) {
            foreach ($fields as $field) {
                $nullCount = $this->getNullCount($table, $field);
                if ($nullCount > 0) {
                    echo "⚠ Warning: {$nullCount} NULL values in {$table}.{$field}\n";
                }
            }
        }
    }

    private function validateRelationships() {
        // Validate foreign key relationships
        $relationships = [
            'accommodation_destinations' => [
                'accommodation_id' => 'accommodation.id',
                'destination_id' => 'destinations.id'
            ],
            'activities_destinations' => [
                'activity_id' => 'activities.id',
                'destination_id' => 'destinations.id'
            ]
        ];

        foreach ($relationships as $table => $foreignKeys) {
            $this->validateForeignKeys($table, $foreignKeys);
        }
    }
}
```

**Tasks**:
1. **Database Export and Analysis**
   - Create comprehensive backup of current MariaDB database
   - Analyze schema for MySQL 8.0 compatibility issues
   - Document any custom functions or procedures
   - Identify potential data type conversion requirements

2. **Schema Migration**
   - Convert MyISAM tables to InnoDB for better performance
   - Update deprecated MySQL syntax for version 8.0 compatibility
   - Recreate indexes and constraints
   - Test stored procedures and triggers

3. **Data Validation**
   - Verify record counts match between source and target
   - Validate data integrity and foreign key relationships
   - Check for character encoding issues
   - Confirm image file references are intact

**Deliverables**:
- Migrated database in RDS with validated data integrity
- Migration validation report with record counts and integrity checks
- Rollback procedures and backup verification
- Performance baseline measurements

#### **Phase 4: Deployment Pipeline and Monitoring (Days 8-9)**

**Objective**: Establish automated deployment pipeline with comprehensive monitoring

**CI/CD Pipeline Configuration**:
```yaml
# .github/workflows/deploy.yml
name: Deploy to AWS Elastic Beanstalk

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup PHP 8.1
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1'
        extensions: mysqli, pdo_mysql, gd, zip

    - name: Install Composer dependencies
      run: composer install --no-dev --optimize-autoloader

    - name: Build assets
      run: |
        npm install
        npm run build

    - name: Create deployment package
      run: |
        zip -r deploy.zip . -x "*.git*" "node_modules/*" "tests/*"

    - name: Deploy to Elastic Beanstalk
      uses: einaregilsson/beanstalk-deploy@v21
      with:
        aws_access_key: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws_secret_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        application_name: bon-voyage-wp
        environment_name: production
        version_label: ${{ github.sha }}
        region: eu-west-2
        deployment_package: deploy.zip
```

**Monitoring Configuration**:
```yaml
# cloudwatch-alarms.yml
Resources:
  HighCPUAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: BonVoyage-HighCPU
      AlarmDescription: Alert when CPU exceeds 80%
      MetricName: CPUUtilization
      Namespace: AWS/EC2
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 80
      ComparisonOperator: GreaterThanThreshold
      AlarmActions:
        - !Ref SNSTopicArn

  DatabaseConnectionAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: BonVoyage-HighDBConnections
      MetricName: DatabaseConnections
      Namespace: AWS/RDS
      Statistic: Average
      Period: 300
      Threshold: 40
      ComparisonOperator: GreaterThanThreshold

  ApplicationErrorAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: BonVoyage-ApplicationErrors
      MetricName: 5XXError
      Namespace: AWS/ApplicationELB
      Statistic: Sum
      Period: 300
      Threshold: 10
      ComparisonOperator: GreaterThanThreshold
```

**Tasks**:
1. **Deployment Automation**
   - Set up GitHub Actions or GitLab CI pipeline
   - Configure automated testing and code quality checks
   - Implement blue-green deployment strategy
   - Create deployment rollback procedures

2. **Monitoring and Alerting**
   - Configure CloudWatch metrics and alarms
   - Set up log aggregation and analysis
   - Implement health check endpoints
   - Configure SNS notifications for critical alerts

3. **Security Hardening**
   - Implement WAF rules for common attacks
   - Configure SSL/TLS certificates
   - Set up security scanning and vulnerability assessment
   - Review and update IAM policies

**Deliverables**:
- Automated deployment pipeline with testing
- Comprehensive monitoring and alerting system
- Security hardening implementation
- Deployment and rollback procedures documentation

#### **Phase 5: Go-Live Support and Handover (Day 10)**

**Objective**: Execute production deployment with monitoring and documentation handover

**Go-Live Checklist**:
```bash
#!/bin/bash
# go-live-checklist.sh

echo "Pre-deployment checklist:"
echo "□ Database backup completed and verified"
echo "□ DNS TTL reduced to 300 seconds"
echo "□ Staging environment tested and approved"
echo "□ Rollback procedures tested"
echo "□ Monitoring alerts configured"
echo "□ Team notifications sent"

echo "Deployment execution:"
echo "□ Deploy application to production"
echo "□ Update DNS records"
echo "□ Verify application functionality"
echo "□ Monitor system metrics"
echo "□ Confirm backup procedures"

echo "Post-deployment validation:"
echo "□ All critical user journeys tested"
echo "□ Performance metrics within acceptable range"
echo "□ No critical errors in logs"
echo "□ Monitoring alerts functioning"
echo "□ Documentation updated"
```

**Tasks**:
1. **Production Deployment**
   - Execute final deployment to production environment
   - Update DNS records for domain cutover
   - Monitor system performance and error rates
   - Verify all functionality is working correctly

2. **Documentation and Handover**
   - Complete technical documentation
   - Provide access credentials and procedures
   - Conduct knowledge transfer session
   - Set up ongoing support arrangements

**Deliverables**:
- Live production environment
- Complete technical documentation
- Access credentials and procedures
- Support and maintenance plan

### Workstream B: WordPress Development & Migration

#### **Phase 1: CakePHP Analysis and WordPress Architecture Planning (Days 1-3)**

**Objective**: Comprehensive analysis of existing CakePHP system and design WordPress architecture

**CakePHP Codebase Analysis**:
```php
// Current CakePHP 1.2.12 structure analysis
app/
├── controllers/
│   ├── destinations_controller.php    # 589 destinations with hierarchy
│   ├── accommodation_controller.php   # 671 hotels with complex relationships
│   ├── activities_controller.php      # 719 activities with destinations
│   ├── travel_plans_controller.php    # Lead generation forms
│   └── pages_controller.php          # CMS pages with tree structure
├── models/
│   ├── destination.php               # Tree behavior, complex associations
│   ├── accommodation.php             # HABTM with destinations, holiday_types
│   ├── activity.php                  # Many-to-many relationships
│   └── travel_plan.php              # Form handling with CRM integration
├── views/
│   ├── destinations/                 # Hierarchical destination pages
│   ├── accommodation/                # Hotel listing and detail pages
│   └── travel_plans/                # Multi-step form interface
└── components/
    ├── navigation_component.php      # Mega menu and mobile menu
    ├── filter_component.php         # Search and filtering
    └── history_component.php        # Breadcrumb navigation
```

**WordPress Architecture Design**:
```php
// Target WordPress structure
wp-content/
├── themes/bonvoyage/
│   ├── functions.php                 # Theme functionality
│   ├── index.php                     # Blog listing
│   ├── single-destination.php       # Destination detail pages
│   ├── single-accommodation.php     # Hotel detail pages
│   ├── archive-destination.php      # Destination listings
│   └── page-travel-plans.php       # Travel planning form
├── plugins/
│   ├── advanced-custom-fields-pro/  # Custom field management
│   ├── gravityforms/                # Form handling
│   └── bon-voyage-functionality/    # Custom plugin for business logic
└── mu-plugins/
    └── bon-voyage-post-types.php    # Custom post type registration
```

**Custom Post Types Design**:
```php
// Custom post types for WordPress migration
function register_bon_voyage_post_types() {

    // Destinations (hierarchical)
    register_post_type('destination', [
        'public' => true,
        'hierarchical' => true,
        'supports' => ['title', 'editor', 'thumbnail', 'page-attributes'],
        'has_archive' => true,
        'rewrite' => ['slug' => 'destinations'],
        'menu_icon' => 'dashicons-location-alt'
    ]);

    // Accommodation
    register_post_type('accommodation', [
        'public' => true,
        'supports' => ['title', 'editor', 'thumbnail'],
        'has_archive' => true,
        'rewrite' => ['slug' => 'accommodation'],
        'menu_icon' => 'dashicons-building'
    ]);

    // Activities
    register_post_type('activity', [
        'public' => true,
        'supports' => ['title', 'editor', 'thumbnail'],
        'has_archive' => true,
        'rewrite' => ['slug' => 'activities'],
        'menu_icon' => 'dashicons-tickets-alt'
    ]);

    // Holiday Types (taxonomy-like but as CPT for complex data)
    register_post_type('holiday_type', [
        'public' => true,
        'supports' => ['title', 'editor', 'thumbnail'],
        'has_archive' => true,
        'rewrite' => ['slug' => 'holiday-types'],
        'menu_icon' => 'dashicons-palmtree'
    ]);

    // Itineraries
    register_post_type('itinerary', [
        'public' => true,
        'supports' => ['title', 'editor', 'thumbnail'],
        'has_archive' => true,
        'rewrite' => ['slug' => 'itineraries'],
        'menu_icon' => 'dashicons-calendar-alt'
    ]);
}
add_action('init', 'register_bon_voyage_post_types');
```

**ACF Field Groups Planning**:
```php
// ACF field groups for complex data structures
$destination_fields = [
    'key' => 'group_destinations',
    'title' => 'Destination Information',
    'fields' => [
        ['key' => 'latitude', 'name' => 'latitude', 'type' => 'number'],
        ['key' => 'longitude', 'name' => 'longitude', 'type' => 'number'],
        ['key' => 'meta_description', 'name' => 'meta_description', 'type' => 'textarea'],
        ['key' => 'youtube_playlist_id', 'name' => 'youtube_playlist_id', 'type' => 'text'],
        ['key' => 'from_price', 'name' => 'from_price', 'type' => 'number'],
        ['key' => 'currency', 'name' => 'currency', 'type' => 'select'],
        ['key' => 'content_blocks', 'name' => 'content_blocks', 'type' => 'flexible_content']
    ],
    'location' => [
        [['param' => 'post_type', 'operator' => '==', 'value' => 'destination']]
    ]
];

$accommodation_fields = [
    'key' => 'group_accommodation',
    'title' => 'Accommodation Details',
    'fields' => [
        ['key' => 'star_rating', 'name' => 'star_rating', 'type' => 'number'],
        ['key' => 'from_price', 'name' => 'from_price', 'type' => 'number'],
        ['key' => 'destinations', 'name' => 'destinations', 'type' => 'relationship'],
        ['key' => 'holiday_types', 'name' => 'holiday_types', 'type' => 'relationship'],
        ['key' => 'featured_image_gallery', 'name' => 'image_gallery', 'type' => 'gallery']
    ],
    'location' => [
        [['param' => 'post_type', 'operator' => '==', 'value' => 'accommodation']]
    ]
];
```

**Tasks**:
1. **CakePHP System Analysis**
   - Document all controllers, models, and their relationships
   - Analyze custom components and helpers functionality
   - Map database schema to WordPress structure
   - Identify third-party integrations and APIs

2. **WordPress Architecture Design**
   - Design custom post types for all content types
   - Plan ACF field groups for complex data structures
   - Design taxonomy structure for categorization
   - Plan URL structure and rewrite rules

3. **Migration Strategy Planning**
   - Create detailed mapping between CakePHP and WordPress
   - Plan data transformation requirements
   - Design content migration scripts architecture
   - Plan testing and validation procedures

**Deliverables**:
- Complete CakePHP system documentation
- WordPress architecture specification
- Custom post types and ACF field definitions
- Migration strategy and data mapping document

#### **Phase 2: WordPress Foundation Setup (Days 4-6)**

**Objective**: Install WordPress and build custom theme with ACF integration

**WordPress Installation and Configuration**:
```php
// wp-config.php production configuration
define('WP_ENV', 'production');
define('WP_HOME', 'https://www.bon-voyage.co.uk');
define('WP_SITEURL', 'https://www.bon-voyage.co.uk/wp');

// Database configuration (from AWS RDS)
define('DB_NAME', getenv('RDS_DB_NAME'));
define('DB_USER', getenv('RDS_USERNAME'));
define('DB_PASSWORD', getenv('RDS_PASSWORD'));
define('DB_HOST', getenv('RDS_HOSTNAME'));

// WordPress security keys (generated)
define('AUTH_KEY', getenv('AUTH_KEY'));
define('SECURE_AUTH_KEY', getenv('SECURE_AUTH_KEY'));
define('LOGGED_IN_KEY', getenv('LOGGED_IN_KEY'));
define('NONCE_KEY', getenv('NONCE_KEY'));

// WordPress debugging (production)
define('WP_DEBUG', false);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);

// File permissions and uploads
define('FS_METHOD', 'direct');
define('UPLOADS', 'wp-content/uploads');

// Memory and performance
define('WP_MEMORY_LIMIT', '256M');
define('WP_MAX_MEMORY_LIMIT', '512M');
```

**Custom Theme Development**:
```php
// functions.php - Theme setup and functionality
function bonvoyage_theme_setup() {
    // Theme support
    add_theme_support('post-thumbnails');
    add_theme_support('html5', ['search-form', 'comment-form', 'comment-list']);
    add_theme_support('title-tag');

    // Image sizes for different content types
    add_image_size('destination-hero', 1200, 600, true);
    add_image_size('accommodation-thumb', 400, 300, true);
    add_image_size('activity-card', 350, 250, true);

    // Navigation menus
    register_nav_menus([
        'primary' => 'Primary Navigation',
        'footer' => 'Footer Navigation',
        'mobile' => 'Mobile Navigation'
    ]);
}
add_action('after_setup_theme', 'bonvoyage_theme_setup');

// Enqueue styles and scripts
function bonvoyage_enqueue_assets() {
    // Main stylesheet
    wp_enqueue_style('bonvoyage-style', get_stylesheet_uri(), [], '1.0.0');

    // Navigation CSS (shared with main site)
    wp_enqueue_style('navigation-css', get_template_directory_uri() . '/assets/css/navigation.css');

    // Main JavaScript
    wp_enqueue_script('bonvoyage-main', get_template_directory_uri() . '/assets/js/main.js', ['jquery'], '1.0.0', true);

    // Navigation JavaScript (shared with main site)
    wp_enqueue_script('navigation-js', get_template_directory_uri() . '/assets/js/navigation.js', ['jquery'], '1.0.0', true);

    // Localize script for AJAX
    wp_localize_script('bonvoyage-main', 'bonvoyage_ajax', [
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('bonvoyage_nonce')
    ]);
}
add_action('wp_enqueue_scripts', 'bonvoyage_enqueue_assets');
```

**Template Development**:
```php
// single-destination.php - Destination detail page template
get_header(); ?>

<div class="page-content-body">
    <div class="page-content-body__content">
        <?php while (have_posts()) : the_post(); ?>
            <div class="destination-hero">
                <?php if (has_post_thumbnail()) : ?>
                    <?php the_post_thumbnail('destination-hero', ['class' => 'destination-hero__image']); ?>
                <?php endif; ?>

                <div class="destination-hero__content">
                    <h1 class="destination-hero__title"><?php the_title(); ?></h1>
                    <?php if (get_field('from_price')) : ?>
                        <div class="destination-hero__price">
                            From <?php echo get_field('currency', 'option'); ?><?php the_field('from_price'); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="destination-content">
                <div class="destination-content__main">
                    <?php the_content(); ?>

                    <?php if (have_rows('content_blocks')) : ?>
                        <div class="content-blocks">
                            <?php while (have_rows('content_blocks')) : the_row(); ?>
                                <?php get_template_part('template-parts/content-block', get_row_layout()); ?>
                            <?php endwhile; ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="destination-content__sidebar">
                    <?php get_template_part('template-parts/destination-sidebar'); ?>
                </div>
            </div>

            <?php
            // Related accommodation
            $related_accommodation = get_field('accommodation');
            if ($related_accommodation) :
                get_template_part('template-parts/related-accommodation', null, ['accommodation' => $related_accommodation]);
            endif;
            ?>

        <?php endwhile; ?>
    </div>
</div>

<?php get_footer(); ?>
```

**Tasks**:
1. **WordPress Core Setup**
   - Install WordPress with production configuration
   - Configure database connection to RDS
   - Set up user roles and permissions
   - Install and configure essential plugins (ACF Pro, Gravity Forms)

2. **Custom Theme Development**
   - Create base theme structure matching current design
   - Develop template hierarchy for all post types
   - Implement responsive design and mobile optimization
   - Integrate shared navigation with main site

3. **ACF Configuration**
   - Create field groups for all custom post types
   - Set up flexible content blocks system
   - Configure relationship fields for complex associations
   - Test field functionality and data entry

**Deliverables**:
- WordPress installation with custom theme
- All custom post types and ACF fields configured
- Template files for all content types
- Responsive design matching current site

#### **Phase 3: Content Migration Development (Days 7-10)**

**Objective**: Develop and execute automated content migration scripts

**Migration Script Architecture**:
```php
<?php
// BonVoyageMigrationScript.php - Main migration controller

class BonVoyageMigrationScript {

    private $sourceDB;
    private $targetDB;
    private $mappings = [];

    public function __construct() {
        $this->sourceDB = new PDO("mysql:host={$_ENV['OLD_DB_HOST']};dbname={$_ENV['OLD_DB_NAME']}",
                                  $_ENV['OLD_DB_USER'], $_ENV['OLD_DB_PASS']);
        $this->targetDB = new PDO("mysql:host={$_ENV['RDS_HOSTNAME']};dbname={$_ENV['RDS_DB_NAME']}",
                                  $_ENV['RDS_USERNAME'], $_ENV['RDS_PASSWORD']);
    }

    public function migrate() {
        $this->logProgress("Starting Bon Voyage migration...");

        try {
            $this->setupWordPressStructure();
            $this->migrateUsers();
            $this->migrateImages();
            $this->migrateDestinations();
            $this->migrateAccommodation();
            $this->migrateActivities();
            $this->migrateHolidayTypes();
            $this->migrateItineraries();
            $this->migratePages();
            $this->migrateSpotlights();
            $this->migrateTravelPlans();
            $this->setupRelationships();
            $this->migrateContentBlocks();
            $this->validateMigration();

            $this->logProgress("Migration completed successfully!");

        } catch (Exception $e) {
            $this->logError("Migration failed: " . $e->getMessage());
            throw $e;
        }
    }

    private function migrateDestinations() {
        $this->logProgress("Migrating destinations...");

        // Get destinations ordered by tree structure (lft ASC)
        $stmt = $this->sourceDB->query("
            SELECT * FROM destinations
            WHERE published = 1
            ORDER BY lft ASC
        ");

        $destinations = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $migrated = 0;

        foreach ($destinations as $dest) {
            $post_id = wp_insert_post([
                'post_title' => $dest['name'],
                'post_name' => $dest['slug'],
                'post_content' => $dest['summary'] ?: '',
                'post_excerpt' => $dest['meta_description'] ?: '',
                'post_type' => 'destination',
                'post_status' => 'publish',
                'post_parent' => $this->getWordPressParent($dest['parent_id']),
                'menu_order' => $dest['lft'] // Preserve tree order
            ]);

            if ($post_id) {
                // Migrate ACF fields
                update_field('latitude', $dest['latitude'], $post_id);
                update_field('longitude', $dest['longitude'], $post_id);
                update_field('meta_description', $dest['meta_description'], $post_id);
                update_field('youtube_playlist_id', $dest['youtube_playlist_id'], $post_id);
                update_field('from_price', $dest['from_price'], $post_id);
                update_field('currency', $dest['currency'], $post_id);

                // Set featured image
                if ($dest['main_image_id']) {
                    $attachment_id = $this->mappings['images'][$dest['main_image_id']] ?? null;
                    if ($attachment_id) {
                        set_post_thumbnail($post_id, $attachment_id);
                    }
                }

                // Store mapping for relationships
                $this->mappings['destinations'][$dest['id']] = $post_id;
                $migrated++;
            }
        }

        $this->logProgress("Migrated {$migrated} destinations");
    }

    private function migrateImages() {
        $this->logProgress("Migrating images...");

        $stmt = $this->sourceDB->query("
            SELECT * FROM images
            WHERE published = 1
        ");

        $images = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $migrated = 0;

        foreach ($images as $image) {
            // Copy physical file
            $oldPath = "/old/app/webroot/img/{$image['id']}.{$image['extension']}";
            $uploadDir = wp_upload_dir();
            $newPath = $uploadDir['path'] . "/{$image['id']}.{$image['extension']}";

            if (file_exists($oldPath)) {
                copy($oldPath, $newPath);

                // Create WordPress attachment
                $attachment_id = wp_insert_attachment([
                    'post_title' => $image['alt'] ?: 'Image ' . $image['id'],
                    'post_content' => '',
                    'post_status' => 'inherit',
                    'post_mime_type' => 'image/' . $image['extension']
                ], $newPath);

                if ($attachment_id) {
                    // Generate metadata
                    require_once(ABSPATH . 'wp-admin/includes/image.php');
                    $metadata = wp_generate_attachment_metadata($attachment_id, $newPath);
                    wp_update_attachment_metadata($attachment_id, $metadata);

                    // Store mapping
                    $this->mappings['images'][$image['id']] = $attachment_id;
                    $migrated++;
                }
            }
        }

        $this->logProgress("Migrated {$migrated} images");
    }

    private function migrateContentBlocks() {
        $this->logProgress("Migrating content blocks...");

        $stmt = $this->sourceDB->query("
            SELECT * FROM content_blocks
            ORDER BY model, modelid, `order`
        ");

        $blocks = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $grouped = [];

        // Group blocks by model and model ID
        foreach ($blocks as $block) {
            $grouped[$block['model']][$block['modelid']][] = $block;
        }

        foreach ($grouped as $model => $modelBlocks) {
            foreach ($modelBlocks as $modelId => $blocks) {
                $postId = $this->getWordPressPostId($model, $modelId);

                if ($postId) {
                    $flexibleContent = [];

                    foreach ($blocks as $block) {
                        $flexibleContent[] = [
                            'acf_fc_layout' => 'content_block',
                            'content' => $block['content'],
                            'image' => $this->mappings['images'][$block['image_id']] ?? null,
                            'alignment' => $block['alignment'],
                            'link_text' => $block['link_text'],
                            'link' => $block['link'],
                            'youtube_video_id' => $block['youtube_video_id']
                        ];
                    }

                    update_field('content_blocks', $flexibleContent, $postId);
                }
            }
        }

        $this->logProgress("Migrated content blocks");
    }

    private function setupRelationships() {
        $this->logProgress("Setting up relationships...");

        // Accommodation to Destinations relationships
        $stmt = $this->sourceDB->query("
            SELECT accommodation_id, destination_id, featured, `order`
            FROM accommodation_destinations
        ");

        $relationships = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($relationships as $rel) {
            $accommodationPost = $this->mappings['accommodation'][$rel['accommodation_id']] ?? null;
            $destinationPost = $this->mappings['destinations'][$rel['destination_id']] ?? null;

            if ($accommodationPost && $destinationPost) {
                // Use ACF Relationship field
                $currentDestinations = get_field('destinations', $accommodationPost) ?: [];
                $currentDestinations[] = $destinationPost;
                update_field('destinations', $currentDestinations, $accommodationPost);

                // Store featured/order metadata
                if ($rel['featured']) {
                    add_post_meta($accommodationPost, 'featured_destination_' . $destinationPost, true);
                }
                add_post_meta($accommodationPost, 'destination_order_' . $destinationPost, $rel['order']);
            }
        }

        $this->logProgress("Relationships configured");
    }
}
```

**Gravity Forms Integration for Travel Plans**:
```php
// Travel Plans migration to Gravity Forms
function migrateTravelPlansToGravityForms() {

    // Create Gravity Form programmatically
    $form = [
        'title' => 'Travel Plans',
        'description' => 'Plan your perfect trip to the USA or Canada',
        'fields' => [
            [
                'type' => 'select',
                'id' => 1,
                'label' => 'Title',
                'choices' => [
                    ['text' => 'Mr', 'value' => 'Mr'],
                    ['text' => 'Mrs', 'value' => 'Mrs'],
                    ['text' => 'Miss', 'value' => 'Miss'],
                    ['text' => 'Ms', 'value' => 'Ms']
                ],
                'isRequired' => true
            ],
            [
                'type' => 'text',
                'id' => 2,
                'label' => 'First Name',
                'isRequired' => true
            ],
            [
                'type' => 'text',
                'id' => 3,
                'label' => 'Last Name',
                'isRequired' => true
            ],
            [
                'type' => 'email',
                'id' => 4,
                'label' => 'Email Address',
                'isRequired' => true
            ],
            [
                'type' => 'phone',
                'id' => 5,
                'label' => 'Telephone Number',
                'isRequired' => true
            ],
            [
                'type' => 'select',
                'id' => 6,
                'label' => 'Destination Country',
                'choices' => [
                    ['text' => 'USA', 'value' => 'USA'],
                    ['text' => 'Canada', 'value' => 'Canada']
                ],
                'isRequired' => true
            ]
            // ... additional fields
        ]
    ];

    $form_id = GFAPI::add_form($form);

    // Migrate existing travel plan submissions
    $stmt = $sourceDB->query("SELECT * FROM travel_plans");
    $travelPlans = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($travelPlans as $plan) {
        $entry = [
            'form_id' => $form_id,
            'date_created' => $plan['created'],
            'is_starred' => 0,
            'is_read' => 1,
            'ip' => '127.0.0.1',
            'source_url' => 'https://www.bon-voyage.co.uk/travel_plans/add',
            'user_agent' => 'Migration Script',
            '1' => $plan['title'],
            '2' => $plan['first_name'],
            '3' => $plan['last_name'],
            '4' => $plan['email_address'],
            '5' => $plan['telephone_number'],
            '6' => $plan['destination_country']
            // ... map all fields
        ];

        GFAPI::add_entry($entry);
    }
}
```

**Tasks**:
1. **Migration Script Development**
   - Build comprehensive migration script with error handling
   - Implement data validation and integrity checks
   - Create mapping system for ID relationships
   - Add progress logging and rollback capabilities

2. **Content Migration Execution**
   - Migrate all destinations with hierarchical structure
   - Transfer accommodation with complex relationships
   - Move activities and holiday types
   - Convert content blocks to ACF flexible content

3. **Form Migration**
   - Create Gravity Forms for travel plans and contact forms
   - Migrate existing form submissions as entries
   - Set up form notifications and CRM integration
   - Test form functionality and validation

**Deliverables**:
- Complete content migration with validated data integrity
- All images transferred and properly linked
- Gravity Forms configured with existing submissions
- Relationship mappings preserved and functional

#### **Phase 4: Integration Testing and Third-Party Services (Days 11-13)**

**Objective**: Integrate third-party services and conduct comprehensive testing

**Third-Party Integrations**:
```php
// Feefo Reviews Integration
class FeefoIntegration {

    private $apiKey;
    private $merchantId;

    public function __construct() {
        $this->apiKey = get_option('feefo_api_key');
        $this->merchantId = get_option('feefo_merchant_id');
    }

    public function getReviews($limit = 10) {
        $url = "https://api.feefo.com/api/10/reviews/merchant/{$this->merchantId}";
        $response = wp_remote_get($url . "?limit={$limit}", [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->apiKey
            ]
        ]);

        if (is_wp_error($response)) {
            return [];
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        return $data['reviews'] ?? [];
    }

    public function displayReviews($atts) {
        $atts = shortcode_atts([
            'limit' => 5,
            'template' => 'default'
        ], $atts);

        $reviews = $this->getReviews($atts['limit']);

        ob_start();
        foreach ($reviews as $review) {
            echo '<div class="feefo-review">';
            echo '<div class="feefo-review__rating">' . str_repeat('★', $review['rating']) . '</div>';
            echo '<div class="feefo-review__text">' . esc_html($review['description']) . '</div>';
            echo '<div class="feefo-review__author">' . esc_html($review['customer']['display_name']) . '</div>';
            echo '</div>';
        }
        return ob_get_clean();
    }
}

// Register Feefo shortcode
add_shortcode('feefo_reviews', [new FeefoIntegration(), 'displayReviews']);
```

**Search Functionality**:
```php
// Enhanced WordPress search for destinations and accommodation
function bonvoyage_custom_search($query) {
    if (!is_admin() && $query->is_main_query() && $query->is_search()) {
        $query->set('post_type', ['post', 'destination', 'accommodation', 'activity']);

        // Add meta query for location-based search
        if (isset($_GET['location'])) {
            $meta_query = [
                'relation' => 'OR',
                [
                    'key' => 'location',
                    'value' => sanitize_text_field($_GET['location']),
                    'compare' => 'LIKE'
                ],
                [
                    'key' => 'destinations',
                    'value' => sanitize_text_field($_GET['location']),
                    'compare' => 'LIKE'
                ]
            ];
            $query->set('meta_query', $meta_query);
        }
    }
}
add_action('pre_get_posts', 'bonvoyage_custom_search');

// AJAX search functionality
function bonvoyage_ajax_search() {
    check_ajax_referer('bonvoyage_nonce', 'nonce');

    $search_term = sanitize_text_field($_POST['search_term']);
    $post_type = sanitize_text_field($_POST['post_type']) ?: 'any';

    $args = [
        's' => $search_term,
        'post_type' => $post_type,
        'posts_per_page' => 10,
        'post_status' => 'publish'
    ];

    $query = new WP_Query($args);
    $results = [];

    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            $results[] = [
                'id' => get_the_ID(),
                'title' => get_the_title(),
                'url' => get_permalink(),
                'excerpt' => get_the_excerpt(),
                'thumbnail' => get_the_post_thumbnail_url(get_the_ID(), 'thumbnail')
            ];
        }
    }

    wp_reset_postdata();
    wp_send_json_success($results);
}
add_action('wp_ajax_bonvoyage_search', 'bonvoyage_ajax_search');
add_action('wp_ajax_nopriv_bonvoyage_search', 'bonvoyage_ajax_search');
```

**Performance Optimization**:
```php
// Caching and performance optimizations
function bonvoyage_performance_optimizations() {

    // Enable object caching for expensive queries
    function get_cached_destinations($parent_id = 0) {
        $cache_key = 'destinations_' . $parent_id;
        $destinations = wp_cache_get($cache_key, 'bonvoyage');

        if (false === $destinations) {
            $destinations = get_posts([
                'post_type' => 'destination',
                'post_parent' => $parent_id,
                'posts_per_page' => -1,
                'orderby' => 'menu_order',
                'order' => 'ASC'
            ]);

            wp_cache_set($cache_key, $destinations, 'bonvoyage', HOUR_IN_SECONDS);
        }

        return $destinations;
    }

    // Optimize database queries
    function optimize_relationship_queries() {
        // Use WP_Query with meta_query instead of multiple get_field() calls
        global $wpdb;

        $results = $wpdb->get_results($wpdb->prepare("
            SELECT p.ID, p.post_title, pm.meta_value as destinations
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = 'destinations'
            WHERE p.post_type = 'accommodation'
            AND p.post_status = 'publish'
            AND pm.meta_value LIKE %s
        ", '%' . $destination_id . '%'));

        return $results;
    }

    // Lazy load images
    function add_lazy_loading() {
        add_filter('wp_get_attachment_image_attributes', function($attr) {
            $attr['loading'] = 'lazy';
            return $attr;
        });
    }
    add_action('init', 'add_lazy_loading');
}
add_action('init', 'bonvoyage_performance_optimizations');
```

**Tasks**:
1. **Third-Party Service Integration**
   - Integrate Feefo reviews API with caching
   - Set up Google APIs for maps and analytics
   - Configure ReCaptcha for form protection
   - Test CRM integration for lead generation

2. **Search and Navigation**
   - Implement enhanced search functionality
   - Set up AJAX search with autocomplete
   - Configure navigation menus and breadcrumbs
   - Test mobile navigation and responsive design

3. **Performance Testing**
   - Implement caching strategies
   - Optimize database queries
   - Test page load speeds
   - Configure CDN integration

**Deliverables**:
- All third-party integrations functional and tested
- Enhanced search and navigation systems
- Performance optimizations implemented
- Mobile responsiveness verified

#### **Phase 5: Final Testing and Launch Preparation (Days 14-16)**

**Objective**: Comprehensive testing, SEO validation, and launch preparation

**Testing Checklist**:
```bash
#!/bin/bash
# comprehensive-testing.sh

echo "=== Bon Voyage WordPress Testing Checklist ==="

echo "□ Content Migration Validation"
echo "  □ All destinations migrated (expected: 589)"
echo "  □ All accommodation migrated (expected: 671)"
echo "  □ All activities migrated (expected: 719)"
echo "  □ All images transferred (expected: 7,887)"
echo "  □ Content blocks converted to ACF"

echo "□ Functionality Testing"
echo "  □ Travel plans form submission"
echo "  □ Contact form functionality"
echo "  □ Search functionality"
echo "  □ Navigation menus"
echo "  □ Mobile responsiveness"

echo "□ Third-Party Integrations"
echo "  □ Feefo reviews display"
echo "  □ Google Analytics tracking"
echo "  □ ReCaptcha protection"
echo "  □ CRM lead submission"

echo "□ Performance Testing"
echo "  □ Page load speed < 3 seconds"
echo "  □ Database query optimization"
echo "  □ Image optimization"
echo "  □ CDN functionality"

echo "□ SEO Validation"
echo "  □ URL redirects working"
echo "  □ Meta descriptions preserved"
echo "  □ Structured data markup"
echo "  □ XML sitemap generation"

echo "□ Security Testing"
echo "  □ User permissions correct"
echo "  □ Form validation working"
echo "  □ File upload restrictions"
echo "  □ SSL certificate valid"
```

**SEO Migration**:
```php
// SEO preservation and enhancement
function bonvoyage_seo_migration() {

    // Preserve meta descriptions from CakePHP
    function migrate_meta_descriptions() {
        $posts = get_posts([
            'post_type' => ['destination', 'accommodation', 'activity'],
            'posts_per_page' => -1
        ]);

        foreach ($posts as $post) {
            $meta_description = get_field('meta_description', $post->ID);
            if ($meta_description) {
                update_post_meta($post->ID, '_yoast_wpseo_metadesc', $meta_description);
            }
        }
    }

    // Set up URL redirects from old CakePHP URLs
    function setup_url_redirects() {
        $redirects = [
            '/destinations/view/(.+)' => '/destinations/$1/',
            '/accommodation/view/(.+)' => '/accommodation/$1/',
            '/activities/view/(.+)' => '/activities/$1/',
            '/travel_plans/add' => '/travel-plans/',
            '/pages/view/(.+)' => '/$1/'
        ];

        foreach ($redirects as $old_pattern => $new_url) {
            add_rewrite_rule($old_pattern, $new_url, 'top');
        }

        flush_rewrite_rules();
    }

    // Generate XML sitemap
    function generate_custom_sitemap() {
        $sitemap = '<?xml version="1.0" encoding="UTF-8"?>';
        $sitemap .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';

        $post_types = ['destination', 'accommodation', 'activity', 'post', 'page'];

        foreach ($post_types as $post_type) {
            $posts = get_posts([
                'post_type' => $post_type,
                'posts_per_page' => -1,
                'post_status' => 'publish'
            ]);

            foreach ($posts as $post) {
                $sitemap .= '<url>';
                $sitemap .= '<loc>' . get_permalink($post->ID) . '</loc>';
                $sitemap .= '<lastmod>' . get_the_modified_date('c', $post->ID) . '</lastmod>';
                $sitemap .= '<priority>0.8</priority>';
                $sitemap .= '</url>';
            }
        }

        $sitemap .= '</urlset>';

        file_put_contents(ABSPATH . 'sitemap.xml', $sitemap);
    }
}
add_action('init', 'bonvoyage_seo_migration');
```

**User Training Documentation**:
```markdown
# WordPress Content Management Guide

## Adding New Destinations

1. Navigate to Destinations > Add New
2. Enter destination name and content
3. Set parent destination if applicable
4. Fill in ACF fields:
   - Latitude/Longitude for map display
   - From price and currency
   - YouTube playlist ID
   - Meta description for SEO
5. Add featured image
6. Configure content blocks for rich content
7. Set relationships to accommodation and activities

## Managing Travel Plan Submissions

1. Go to Forms > Entries
2. View travel plan submissions
3. Export data for CRM integration
4. Set up email notifications
5. Configure form confirmations

## Content Block Management

1. Edit any destination or accommodation
2. Scroll to Content Blocks section
3. Add new blocks with layout options:
   - Text Block
   - Image Block
   - Video Block
   - Call-to-Action Block
4. Arrange blocks in desired order
5. Preview changes before publishing
```

**Tasks**:
1. **Comprehensive Testing**
   - Execute full testing checklist
   - Validate all migrated content
   - Test all forms and integrations
   - Verify mobile responsiveness

2. **SEO and Performance**
   - Set up URL redirects from old site
   - Validate meta descriptions and titles
   - Generate XML sitemap
   - Test page load speeds

3. **User Training and Documentation**
   - Create content management documentation
   - Train content editors on WordPress
   - Set up user roles and permissions
   - Provide ongoing support procedures

**Deliverables**:
- Fully tested WordPress application
- SEO migration with preserved rankings
- User training and documentation
- Go-live readiness confirmation

### Database Migration Analysis

#### **Current Database Schema Overview**

The CakePHP application uses a complex database schema with **30+ core tables** and extensive relationships:

**Core Content Tables:**
- `destinations` (589 records) - Hierarchical tree structure with lft/rght
- `accommodation` (671 records) - Hotels/lodging with complex relationships
- `activities` (719 records) - Things to do at destinations
- `holiday_types` (23 records) - Travel categories/themes
- `itineraries` (197 records) - Multi-day travel plans
- `pages` (60 records) - CMS pages with tree structure
- `spotlights` (250 records) - Featured content/offers

**Relationship Tables (Many-to-Many):**
- `accommodation_destinations` (3,087 records)
- `activities_destinations` (3,791 records)
- `destinations_itineraries` (5,625 records)
- `holiday_types_on_destinations` (671 records)
- `accommodation_holiday_types` (741 records)

**Media & Content Management:**
- `images` (7,887 records) - File management system
- `image_versions` (8 records) - Different image sizes/crops
- `content_blocks` (4,986 records) - Flexible content blocks
- `custom_image_versions` - Custom image cropping data

**User & Form Data:**
- `users` (27 records) - Admin users with ACL
- `travel_plans` (18 records) - Lead generation forms
- `quote_requests` (8,981 records) - Legacy quote forms
- `contacts` (3,294 records) - Contact form submissions

#### **Migration Complexity Assessment**

| Component | Complexity | Automation Level | Estimated Time |
|-----------|------------|------------------|----------------|
| **Users & Permissions** | ✅ Low | 95% Automated | 2 hours |
| **Images & Media** | ✅ Medium | 90% Automated | 1 day |
| **Destinations (Hierarchical)** | ⚠️ Medium | 85% Automated | 2 days |
| **Accommodation** | ✅ Medium | 90% Automated | 1 day |
| **Activities & Holiday Types** | ✅ Low | 95% Automated | 4 hours |
| **Itineraries** | ⚠️ Medium | 80% Automated | 1 day |
| **Content Blocks** | ⚠️ Complex | 75% Automated | 2 days |
| **Many-to-Many Relationships** | ⚠️ Complex | 70% Automated | 2 days |
| **Travel Plans → Gravity Forms** | ✅ Medium | 85% Automated | 1 day |
| **URL Redirects & SEO** | ✅ Medium | 90% Automated | 4 hours |

**Total Migration Effort: 10-12 days**

#### **Success Factors**
- ✅ **Clean Data Structure**: Well-normalized database
- ✅ **Standard Patterns**: Common CMS patterns (hierarchies, relationships)
- ✅ **WordPress Flexibility**: ACF handles complex field structures
- ✅ **Existing Tools**: WP-CLI, ACF Pro, Gravity Forms APIs

The database structure is **highly suitable for automated migration** to WordPress + ACF. The well-organized schema, standard relationships, and WordPress's flexible content system make this a **low-risk, high-success** migration path.
